#!/usr/bin/env python3
"""
DuckDuckGo 邮箱注册脚本启动器
"""
import os
import sys
import argparse
from loguru import logger
from duck_email_register import DuckEmailRegister
from utils import Utils

def setup_directories():
    """创建必要的目录"""
    directories = ['logs', 'screenshots', 'results']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def register_single_email(email: str = None, password: str = None):
    """注册单个邮箱"""
    if not email:
        email = Utils.generate_random_email("gmail.com")
    
    if not password:
        password = Utils.generate_random_password()
    
    logger.info(f"开始注册邮箱: {email}")
    
    with DuckEmailRegister() as registrar:
        result = registrar.register(email, password)
        
        if result['success']:
            logger.success("注册成功！")
            print(f"\n✅ 注册成功")
            print(f"📧 邮箱: {result['email']}")
            print(f"🔑 密码: {result['password']}")
            
            # 保存到文件
            with open('results/successful_registrations.txt', 'a', encoding='utf-8') as f:
                f.write(f"{result['email']}:{result['password']}\n")
                
        else:
            logger.error(f"注册失败: {result['message']}")
            print(f"\n❌ 注册失败: {result['message']}")
    
    return result

def register_batch_emails(count: int, delay_range: tuple = (10, 20)):
    """批量注册邮箱"""
    logger.info(f"开始批量注册 {count} 个邮箱")
    
    successful_count = 0
    failed_count = 0
    
    for i in range(count):
        logger.info(f"正在注册第 {i+1}/{count} 个邮箱...")
        
        result = register_single_email()
        
        if result['success']:
            successful_count += 1
        else:
            failed_count += 1
        
        # 添加延迟（除了最后一个）
        if i < count - 1:
            delay = Utils.random_delay(delay_range[0], delay_range[1])
            logger.info(f"等待 {delay:.1f} 秒后继续...")
    
    logger.info(f"批量注册完成: 成功 {successful_count}, 失败 {failed_count}")
    print(f"\n📊 批量注册结果:")
    print(f"✅ 成功: {successful_count}")
    print(f"❌ 失败: {failed_count}")
    print(f"📁 成功的注册信息已保存到: results/successful_registrations.txt")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DuckDuckGo 邮箱注册工具')
    parser.add_argument('--email', '-e', help='指定要注册的邮箱地址')
    parser.add_argument('--password', '-p', help='指定密码（可选）')
    parser.add_argument('--batch', '-b', type=int, help='批量注册数量')
    parser.add_argument('--delay-min', type=int, default=10, help='批量注册最小延迟（秒）')
    parser.add_argument('--delay-max', type=int, default=20, help='批量注册最大延迟（秒）')
    parser.add_argument('--headless', action='store_true', help='无头模式运行')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细日志输出')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logger.remove()
        logger.add(sys.stdout, level="DEBUG")
    
    # 创建必要目录
    setup_directories()
    
    try:
        if args.batch:
            # 批量注册
            register_batch_emails(
                count=args.batch,
                delay_range=(args.delay_min, args.delay_max)
            )
        else:
            # 单个注册
            register_single_email(args.email, args.password)
            
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        print("\n⏹️  操作已中断")
    except Exception as e:
        logger.error(f"程序执行错误: {e}")
        print(f"\n💥 程序错误: {e}")

if __name__ == "__main__":
    main()
