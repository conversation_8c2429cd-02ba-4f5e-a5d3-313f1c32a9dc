#!/usr/bin/env python3
"""
测试脚本 - 验证 DuckDuckGo 邮箱注册脚本安装
"""
import sys
import os
from pathlib import Path

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        from config import Config
        print("✅ config 模块导入成功")
        
        from utils import Utils
        print("✅ utils 模块导入成功")
        
        from duck_email_register import DuckEmailRegister
        print("✅ duck_email_register 模块导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n📦 测试依赖包...")
    
    dependencies = [
        'DrissionPage',
        'fake_useragent',
        'loguru',
        'dotenv',
        'requests'
    ]
    
    all_ok = True
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} 可用")
        except ImportError:
            print(f"❌ {dep} 不可用")
            all_ok = False
    
    return all_ok

def test_config():
    """测试配置"""
    print("\n⚙️ 测试配置...")
    
    try:
        from config import Config
        config = Config()
        
        # 检查基本配置
        assert hasattr(config, 'BROWSER_CONFIG')
        assert hasattr(config, 'TIMEOUTS')
        assert hasattr(config, 'URLS')
        
        print("✅ 配置文件结构正确")
        
        # 检查URL配置
        urls = config.URLS
        assert 'duckduckgo_home' in urls
        assert 'email_protection' in urls
        
        print("✅ URL 配置正确")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_utils():
    """测试工具函数"""
    print("\n🔧 测试工具函数...")
    
    try:
        from utils import Utils
        
        # 测试邮箱生成
        email = Utils.generate_random_email()
        assert '@' in email
        print(f"✅ 邮箱生成: {email}")
        
        # 测试密码生成
        password = Utils.generate_random_password()
        assert len(password) >= 8
        print(f"✅ 密码生成: {password}")
        
        # 测试邮箱验证
        assert Utils.validate_email("<EMAIL>") == True
        assert Utils.validate_email("invalid-email") == False
        print("✅ 邮箱验证功能正常")
        
        return True
    except Exception as e:
        print(f"❌ 工具函数测试失败: {e}")
        return False

def test_directories():
    """测试目录结构"""
    print("\n📁 测试目录结构...")
    
    required_dirs = ['logs', 'screenshots', 'results']
    all_ok = True
    
    for directory in required_dirs:
        path = Path(directory)
        if path.exists():
            print(f"✅ {directory}/ 目录存在")
        else:
            print(f"⚠️ {directory}/ 目录不存在，正在创建...")
            path.mkdir(exist_ok=True)
            print(f"✅ {directory}/ 目录已创建")
    
    return all_ok

def test_browser_compatibility():
    """测试浏览器兼容性"""
    print("\n🌐 测试浏览器兼容性...")
    
    try:
        from DrissionPage import ChromiumOptions
        
        # 创建基本选项
        options = ChromiumOptions()
        options.headless()
        
        print("✅ DrissionPage 浏览器选项创建成功")
        return True
    except Exception as e:
        print(f"❌ 浏览器兼容性测试失败: {e}")
        return False

def test_file_permissions():
    """测试文件权限"""
    print("\n🔐 测试文件权限...")
    
    try:
        # 测试日志文件写入
        log_file = Path("logs/test.log")
        log_file.parent.mkdir(exist_ok=True)
        log_file.write_text("test")
        log_file.unlink()
        print("✅ 日志文件写入权限正常")
        
        # 测试截图文件写入
        screenshot_file = Path("screenshots/test.txt")
        screenshot_file.parent.mkdir(exist_ok=True)
        screenshot_file.write_text("test")
        screenshot_file.unlink()
        print("✅ 截图文件写入权限正常")
        
        return True
    except Exception as e:
        print(f"❌ 文件权限测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 DuckDuckGo 邮箱注册脚本 - 安装测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("依赖包", test_dependencies),
        ("配置", test_config),
        ("工具函数", test_utils),
        ("目录结构", test_directories),
        ("浏览器兼容性", test_browser_compatibility),
        ("文件权限", test_file_permissions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"💥 {test_name} 测试异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！安装成功！")
        print("\n📖 下一步:")
        print("1. 运行 python example.py 查看示例")
        print("2. 运行 python run.py --help 查看命令行选项")
        print("3. 查看 README.md 了解详细使用方法")
        return True
    else:
        print("⚠️ 部分测试失败，请检查安装")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
