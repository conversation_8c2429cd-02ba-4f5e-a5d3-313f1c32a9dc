# DuckDuckGo 邮箱注册脚本 Makefile

.PHONY: help install test run clean setup

# 默认目标
help:
	@echo "DuckDuckGo 邮箱注册脚本"
	@echo "========================"
	@echo "可用命令:"
	@echo "  make install    - 安装依赖和设置环境"
	@echo "  make setup      - 创建必要目录和配置文件"
	@echo "  make test       - 测试安装"
	@echo "  make run        - 运行示例脚本"
	@echo "  make single     - 注册单个邮箱"
	@echo "  make batch      - 批量注册邮箱（默认3个）"
	@echo "  make clean      - 清理生成的文件"
	@echo "  make help       - 显示此帮助信息"

# 安装依赖
install:
	@echo "🚀 开始安装..."
	python3 install.py

# 设置环境
setup:
	@echo "📁 创建目录..."
	mkdir -p logs screenshots results
	@if [ ! -f .env ]; then cp .env.example .env; echo "✅ 创建 .env 文件"; fi
	@echo "✅ 环境设置完成"

# 测试安装
test:
	@echo "🧪 测试安装..."
	python3 -c "from duck_email_register import DuckEmailRegister; from utils import Utils; print('✅ 模块导入成功')"
	python3 -c "from utils import Utils; print('✅ 示例邮箱:', Utils.generate_random_email())"

# 运行示例脚本
run:
	@echo "🎯 运行示例脚本..."
	python3 example.py

# 注册单个邮箱
single:
	@echo "📧 注册单个邮箱..."
	python3 run.py

# 批量注册邮箱
batch:
	@echo "📧 批量注册邮箱..."
	python3 run.py --batch 3

# 批量注册（自定义数量）
batch-custom:
	@read -p "请输入注册数量: " count; \
	python3 run.py --batch $$count

# 清理生成的文件
clean:
	@echo "🧹 清理文件..."
	rm -rf logs/* screenshots/* results/*
	rm -rf __pycache__ *.pyc
	@echo "✅ 清理完成"

# 查看日志
logs:
	@echo "📋 最新日志:"
	@if [ -f logs/duck_register_$(shell date +%Y-%m-%d).log ]; then \
		tail -20 logs/duck_register_$(shell date +%Y-%m-%d).log; \
	else \
		echo "暂无日志文件"; \
	fi

# 查看结果
results:
	@echo "📊 注册结果:"
	@if [ -f results/successful_registrations.txt ]; then \
		echo "成功注册的邮箱:"; \
		cat results/successful_registrations.txt; \
	else \
		echo "暂无成功注册记录"; \
	fi

# 安装开发依赖
dev-install:
	pip3 install -r requirements.txt
	pip3 install pytest black flake8 mypy

# 代码格式化
format:
	black *.py
	@echo "✅ 代码格式化完成"

# 代码检查
lint:
	flake8 *.py --max-line-length=100
	mypy *.py --ignore-missing-imports
	@echo "✅ 代码检查完成"

# 完整安装（包括测试）
full-install: install setup test
	@echo "🎉 完整安装完成！"
