"""
DuckDuckGo 邮箱注册脚本
使用 DrissionPage 自动化注册 @duck.com 邮箱
"""
import os
import sys
import time
from typing import Optional, Dict, Any
from loguru import logger
from DrissionPage import ChromiumPage, ChromiumOptions
from config import Config
from utils import Utils

class DuckEmailRegister:
    """DuckDuckGo 邮箱注册器"""
    
    def __init__(self, config: Config = None):
        """初始化注册器"""
        self.config = config or Config()
        self.page: Optional[ChromiumPage] = None
        self.setup_logger()
        
    def setup_logger(self):
        """设置日志"""
        logger.remove()  # 移除默认处理器
        logger.add(
            sys.stdout,
            level=self.config.LOG_CONFIG['level'],
            format=self.config.LOG_CONFIG['format']
        )
        logger.add(
            "logs/duck_register_{time:YYYY-MM-DD}.log",
            level=self.config.LOG_CONFIG['level'],
            format=self.config.LOG_CONFIG['format'],
            rotation=self.config.LOG_CONFIG['rotation'],
            retention=self.config.LOG_CONFIG['retention']
        )
        
    def setup_browser(self) -> bool:
        """设置浏览器"""
        try:
            # 创建浏览器选项
            options = ChromiumOptions()
            
            # 基本配置
            if self.config.BROWSER_CONFIG['headless']:
                options.headless()
            
            # 窗口大小
            width, height = self.config.BROWSER_CONFIG['window_size']
            options.set_window_size(width, height)
            
            # 用户代理
            user_agent = Utils.get_random_user_agent()
            options.set_user_agent(user_agent)
            
            # 禁用图片（可选）
            if self.config.BROWSER_CONFIG['disable_images']:
                options.set_pref('profile.managed_default_content_settings.images', 2)
            
            # 其他选项
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 创建页面对象
            self.page = ChromiumPage(addr_or_opts=options)
            
            # 设置超时
            self.page.set.timeouts(
                base=self.config.TIMEOUTS['page_load'],
                page_load=self.config.TIMEOUTS['page_load'],
                script=self.config.TIMEOUTS['page_load']
            )
            
            logger.info("浏览器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"浏览器初始化失败: {e}")
            return False
    
    @Utils.retry_on_exception(max_attempts=3, delay=2.0)
    def navigate_to_signup(self) -> bool:
        """导航到注册页面"""
        try:
            logger.info("正在访问 DuckDuckGo 主页...")
            self.page.get(self.config.URLS['duckduckgo_home'])
            Utils.random_delay(2, 4)
            
            # 查找 Email Protection 链接
            logger.info("查找 Email Protection 链接...")
            email_link_selectors = [
                'a[href*="email"]',
                'a:contains("Email Protection")',
                'a:contains("Email")',
                '.js-email-protection-link',
                '[data-testid="email-protection"]'
            ]
            
            email_link_found = False
            for selector in email_link_selectors:
                if Utils.safe_click(self.page, selector, timeout=5):
                    email_link_found = True
                    logger.info(f"成功点击 Email Protection 链接: {selector}")
                    break
            
            if not email_link_found:
                # 直接访问邮箱保护页面
                logger.info("直接访问 Email Protection 页面...")
                self.page.get(self.config.URLS['email_protection'])
            
            Utils.random_delay(3, 5)
            
            # 查找注册按钮
            logger.info("查找注册按钮...")
            signup_selectors = [
                'button:contains("Get Started")',
                'button:contains("Sign Up")',
                'a:contains("Get Started")',
                'a:contains("Sign Up")',
                '.signup-button',
                '[data-testid="signup-button"]',
                'button[type="submit"]'
            ]
            
            for selector in signup_selectors:
                if Utils.safe_click(self.page, selector, timeout=5):
                    logger.info(f"成功点击注册按钮: {selector}")
                    Utils.random_delay(2, 4)
                    return True
            
            # 如果没有找到注册按钮，尝试直接访问注册页面
            logger.info("直接访问注册页面...")
            self.page.get(self.config.URLS['signup'])
            Utils.random_delay(2, 4)
            
            return True
            
        except Exception as e:
            logger.error(f"导航到注册页面失败: {e}")
            Utils.take_screenshot(self.page, "navigation_error.png")
            return False
    
    @Utils.retry_on_exception(max_attempts=3, delay=2.0)
    def fill_registration_form(self, email: str, password: str = None) -> bool:
        """填写注册表单"""
        try:
            logger.info("开始填写注册表单...")
            
            # 如果没有提供密码，生成一个随机密码
            if not password:
                password = Utils.generate_random_password()
                logger.info(f"生成随机密码: {password}")
            
            # 查找邮箱输入框
            email_selectors = [
                'input[type="email"]',
                'input[name="email"]',
                'input[placeholder*="email"]',
                '#email',
                '.email-input'
            ]
            
            email_filled = False
            for selector in email_selectors:
                if Utils.safe_input(self.page, selector, email, timeout=5):
                    logger.info(f"成功填写邮箱: {selector}")
                    email_filled = True
                    break
            
            if not email_filled:
                logger.error("未找到邮箱输入框")
                return False
            
            # 查找密码输入框（如果需要）
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                'input[placeholder*="password"]',
                '#password',
                '.password-input'
            ]
            
            for selector in password_selectors:
                if Utils.safe_input(self.page, selector, password, timeout=3):
                    logger.info(f"成功填写密码: {selector}")
                    break
            
            Utils.random_delay(1, 2)
            
            # 查找并点击提交按钮
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("Sign Up")',
                'button:contains("Register")',
                'button:contains("Create Account")',
                '.submit-button',
                '.register-button'
            ]
            
            for selector in submit_selectors:
                if Utils.safe_click(self.page, selector, timeout=5):
                    logger.info(f"成功点击提交按钮: {selector}")
                    Utils.random_delay(3, 5)
                    return True
            
            logger.error("未找到提交按钮")
            return False
            
        except Exception as e:
            logger.error(f"填写注册表单失败: {e}")
            Utils.take_screenshot(self.page, "form_fill_error.png")
            return False

    def handle_verification(self) -> bool:
        """处理验证步骤"""
        try:
            logger.info("检查是否需要验证...")

            # 等待页面加载
            Utils.random_delay(3, 5)

            # 检查是否有验证码
            captcha_selectors = [
                '.captcha',
                '.recaptcha',
                '#captcha',
                '[data-testid="captcha"]',
                'iframe[src*="recaptcha"]'
            ]

            for selector in captcha_selectors:
                if Utils.wait_for_element(self.page, selector, timeout=3):
                    logger.warning("检测到验证码，需要手动处理")
                    input("请手动完成验证码，然后按回车继续...")
                    break

            # 检查是否需要邮箱验证
            verification_indicators = [
                'text:contains("verify")',
                'text:contains("confirmation")',
                'text:contains("check your email")',
                '.verification-message',
                '.email-verification'
            ]

            for indicator in verification_indicators:
                if Utils.wait_for_element(self.page, indicator, timeout=3):
                    logger.info("检测到邮箱验证要求")
                    return True

            return True

        except Exception as e:
            logger.error(f"处理验证失败: {e}")
            return False

    def check_registration_success(self) -> bool:
        """检查注册是否成功"""
        try:
            logger.info("检查注册结果...")

            # 成功指示器
            success_indicators = [
                'text:contains("success")',
                'text:contains("welcome")',
                'text:contains("account created")',
                'text:contains("registration complete")',
                '.success-message',
                '.welcome-message'
            ]

            for indicator in success_indicators:
                if Utils.wait_for_element(self.page, indicator, timeout=10):
                    logger.success("注册成功！")
                    return True

            # 错误指示器
            error_indicators = [
                'text:contains("error")',
                'text:contains("failed")',
                'text:contains("invalid")',
                'text:contains("already exists")',
                '.error-message',
                '.alert-error'
            ]

            for indicator in error_indicators:
                if Utils.wait_for_element(self.page, indicator, timeout=5):
                    error_element = self.page.ele(indicator)
                    error_text = error_element.text if error_element else "未知错误"
                    logger.error(f"注册失败: {error_text}")
                    return False

            # 如果没有明确的成功或失败指示器，检查URL变化
            current_url = self.page.url
            if 'success' in current_url.lower() or 'welcome' in current_url.lower():
                logger.success("注册成功（根据URL判断）")
                return True

            logger.warning("无法确定注册结果")
            return False

        except Exception as e:
            logger.error(f"检查注册结果失败: {e}")
            return False

    def register(self, email: str, password: str = None) -> Dict[str, Any]:
        """执行完整的注册流程"""
        result = {
            'success': False,
            'email': email,
            'password': password,
            'message': '',
            'screenshot': ''
        }

        try:
            logger.info(f"开始注册 DuckDuckGo 邮箱: {email}")

            # 验证邮箱格式
            if not Utils.validate_email(email):
                result['message'] = "邮箱格式无效"
                logger.error(result['message'])
                return result

            # 设置浏览器
            if not self.setup_browser():
                result['message'] = "浏览器初始化失败"
                return result

            # 导航到注册页面
            if not self.navigate_to_signup():
                result['message'] = "导航到注册页面失败"
                result['screenshot'] = Utils.take_screenshot(self.page, "navigation_failed.png")
                return result

            # 填写注册表单
            if not self.fill_registration_form(email, password):
                result['message'] = "填写注册表单失败"
                result['screenshot'] = Utils.take_screenshot(self.page, "form_failed.png")
                return result

            # 处理验证
            if not self.handle_verification():
                result['message'] = "处理验证失败"
                result['screenshot'] = Utils.take_screenshot(self.page, "verification_failed.png")
                return result

            # 检查注册结果
            if self.check_registration_success():
                result['success'] = True
                result['message'] = "注册成功"
                result['screenshot'] = Utils.take_screenshot(self.page, "success.png")
                logger.success(f"成功注册 DuckDuckGo 邮箱: {email}")
            else:
                result['message'] = "注册失败"
                result['screenshot'] = Utils.take_screenshot(self.page, "failed.png")

            return result

        except Exception as e:
            result['message'] = f"注册过程中发生错误: {e}"
            logger.error(result['message'])
            if self.page:
                result['screenshot'] = Utils.take_screenshot(self.page, "error.png")
            return result

        finally:
            self.cleanup()

    def cleanup(self):
        """清理资源"""
        try:
            if self.page:
                self.page.quit()
                logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"清理资源失败: {e}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()
