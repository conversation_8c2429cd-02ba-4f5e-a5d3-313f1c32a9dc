"""
配置文件 - DuckDuckGo 邮箱注册脚本
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """配置类"""
    
    # 浏览器配置
    BROWSER_CONFIG = {
        'headless': False,  # 是否无头模式
        'window_size': (1920, 1080),  # 窗口大小
        'user_data_dir': None,  # 用户数据目录
        'disable_images': False,  # 是否禁用图片加载
        'disable_javascript': False,  # 是否禁用JavaScript
    }
    
    # 超时设置
    TIMEOUTS = {
        'page_load': 30,  # 页面加载超时
        'element_wait': 10,  # 元素等待超时
        'implicit_wait': 5,  # 隐式等待
    }
    
    # 延迟设置（秒）
    DELAYS = {
        'min_action': 1,  # 最小操作延迟
        'max_action': 3,  # 最大操作延迟
        'between_steps': 2,  # 步骤间延迟
    }
    
    # URL配置
    URLS = {
        'duckduckgo_home': 'https://duckduckgo.com',
        'email_protection': 'https://duckduckgo.com/email',
        'signup': 'https://duckduckgo.com/email/signup',
    }
    
    # 用户代理配置
    USER_AGENTS = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    ]
    
    # 日志配置
    LOG_CONFIG = {
        'level': 'INFO',
        'format': '{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}',
        'rotation': '10 MB',
        'retention': '7 days',
    }
    
    # 代理配置（可选）
    PROXY = {
        'enabled': False,
        'http': os.getenv('HTTP_PROXY'),
        'https': os.getenv('HTTPS_PROXY'),
    }
    
    # 重试配置
    RETRY_CONFIG = {
        'max_attempts': 3,
        'backoff_factor': 2,
        'exceptions': ['TimeoutException', 'NoSuchElementException', 'WebDriverException']
    }
