# DuckDuckGo 邮箱注册脚本

使用 DrissionPage 自动化注册 @duck.com 邮箱的 Python 脚本。

## 功能特性

- 🚀 基于 DrissionPage 的高效浏览器自动化
- 🔄 智能重试机制和错误处理
- 📝 详细的日志记录和截图功能
- 🎲 随机用户代理和延迟，避免被检测
- 📧 支持单个和批量邮箱注册
- ⚙️ 灵活的配置选项
- 🛡️ 安全的密码生成

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 基本使用

```python
from duck_email_register import DuckEmailRegister
from utils import Utils

# 生成随机邮箱
email = Utils.generate_random_email("gmail.com")

# 创建注册器并执行注册
with DuckEmailRegister() as registrar:
    result = registrar.register(email)
    
    if result['success']:
        print(f"注册成功: {result['email']}")
        print(f"密码: {result['password']}")
    else:
        print(f"注册失败: {result['message']}")
```

### 2. 运行示例脚本

```bash
python example.py
```

### 3. 自定义配置

复制 `.env.example` 为 `.env` 并修改配置：

```bash
cp .env.example .env
```

## 配置选项

### 浏览器配置

- `headless`: 是否无头模式运行
- `window_size`: 浏览器窗口大小
- `disable_images`: 是否禁用图片加载
- `user_agent`: 自定义用户代理

### 超时设置

- `page_load`: 页面加载超时时间
- `element_wait`: 元素等待超时时间
- `implicit_wait`: 隐式等待时间

### 延迟设置

- `min_action`: 最小操作延迟
- `max_action`: 最大操作延迟
- `between_steps`: 步骤间延迟

## API 文档

### DuckEmailRegister 类

#### 方法

- `register(email, password=None)`: 执行完整注册流程
- `setup_browser()`: 初始化浏览器
- `navigate_to_signup()`: 导航到注册页面
- `fill_registration_form(email, password)`: 填写注册表单
- `handle_verification()`: 处理验证步骤
- `check_registration_success()`: 检查注册结果

#### 返回值

```python
{
    'success': bool,      # 是否成功
    'email': str,         # 邮箱地址
    'password': str,      # 密码
    'message': str,       # 结果消息
    'screenshot': str     # 截图文件路径
}
```

### Utils 工具类

#### 方法

- `generate_random_username(length)`: 生成随机用户名
- `generate_random_email(domain)`: 生成随机邮箱
- `generate_random_password(length)`: 生成随机密码
- `validate_email(email)`: 验证邮箱格式
- `random_delay(min_seconds, max_seconds)`: 随机延迟
- `get_random_user_agent()`: 获取随机用户代理

## 注意事项

⚠️ **重要提醒**

1. **合规使用**: 本脚本仅供学习和测试目的，请遵守 DuckDuckGo 的服务条款
2. **频率限制**: 避免频繁注册，建议在批量操作时添加适当延迟
3. **验证码处理**: 遇到验证码时需要手动处理
4. **网络环境**: 确保网络连接稳定，必要时可配置代理
5. **浏览器版本**: 确保 Chrome/Chromium 浏览器版本与 DrissionPage 兼容

## 故障排除

### 常见问题

1. **浏览器启动失败**
   - 检查 Chrome/Chromium 是否正确安装
   - 尝试更新 DrissionPage 版本

2. **元素定位失败**
   - 网页结构可能已更新，需要调整选择器
   - 检查网络连接和页面加载状态

3. **验证码问题**
   - 脚本会暂停等待手动处理验证码
   - 可以考虑使用代理或更换网络环境

4. **注册失败**
   - 检查邮箱格式是否正确
   - 确认 DuckDuckGo 服务是否正常

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途或违反服务条款的行为。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 更新日志

- v1.0.0: 初始版本，支持基本的邮箱注册功能
- 支持单个和批量注册
- 添加详细的日志和截图功能
- 实现智能重试机制
