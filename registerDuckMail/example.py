"""
DuckDuckGo 邮箱注册示例脚本
"""
import os
import sys
from loguru import logger
from duck_email_register import Duck<PERSON>mail<PERSON>egister
from utils import Utils

def single_registration_example():
    """单个邮箱注册示例"""
    logger.info("=== 单个邮箱注册示例 ===")
    
    # 生成随机邮箱或使用指定邮箱
    email = Utils.generate_random_email("gmail.com")
    password = Utils.generate_random_password()
    
    logger.info(f"准备注册邮箱: {email}")
    logger.info(f"使用密码: {password}")
    
    # 使用上下文管理器确保资源正确释放
    with DuckEmailRegister() as registrar:
        result = registrar.register(email, password)
        
        if result['success']:
            logger.success(f"注册成功！")
            logger.info(f"邮箱: {result['email']}")
            logger.info(f"密码: {result['password']}")
            if result['screenshot']:
                logger.info(f"截图: {result['screenshot']}")
        else:
            logger.error(f"注册失败: {result['message']}")
            if result['screenshot']:
                logger.info(f"错误截图: {result['screenshot']}")
    
    return result

def batch_registration_example(count: int = 3):
    """批量邮箱注册示例"""
    logger.info(f"=== 批量邮箱注册示例 (数量: {count}) ===")
    
    results = []
    
    for i in range(count):
        logger.info(f"开始第 {i+1}/{count} 个邮箱注册...")
        
        # 生成随机邮箱
        email = Utils.generate_random_email("gmail.com")
        password = Utils.generate_random_password()
        
        with DuckEmailRegister() as registrar:
            result = registrar.register(email, password)
            results.append(result)
            
            if result['success']:
                logger.success(f"第 {i+1} 个邮箱注册成功: {email}")
            else:
                logger.error(f"第 {i+1} 个邮箱注册失败: {email} - {result['message']}")
        
        # 批量注册时添加延迟避免被检测
        if i < count - 1:
            logger.info("等待一段时间后继续...")
            Utils.random_delay(10, 20)
    
    # 统计结果
    successful = sum(1 for r in results if r['success'])
    failed = len(results) - successful
    
    logger.info(f"=== 批量注册完成 ===")
    logger.info(f"成功: {successful}/{len(results)}")
    logger.info(f"失败: {failed}/{len(results)}")
    
    # 保存结果到文件
    save_results_to_file(results)
    
    return results

def save_results_to_file(results: list, filename: str = "registration_results.txt"):
    """保存注册结果到文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("DuckDuckGo 邮箱注册结果\n")
            f.write("=" * 50 + "\n\n")
            
            for i, result in enumerate(results, 1):
                f.write(f"第 {i} 个邮箱:\n")
                f.write(f"邮箱: {result['email']}\n")
                f.write(f"密码: {result['password']}\n")
                f.write(f"状态: {'成功' if result['success'] else '失败'}\n")
                f.write(f"消息: {result['message']}\n")
                if result['screenshot']:
                    f.write(f"截图: {result['screenshot']}\n")
                f.write("-" * 30 + "\n\n")
        
        logger.info(f"结果已保存到文件: {filename}")
        
    except Exception as e:
        logger.error(f"保存结果到文件失败: {e}")

def custom_registration_example():
    """自定义邮箱注册示例"""
    logger.info("=== 自定义邮箱注册示例 ===")
    
    # 可以指定具体的邮箱和密码
    email = input("请输入要注册的邮箱地址: ").strip()
    password = input("请输入密码（留空自动生成）: ").strip()
    
    if not email:
        logger.error("邮箱地址不能为空")
        return None
    
    if not password:
        password = Utils.generate_random_password()
        logger.info(f"自动生成密码: {password}")
    
    with DuckEmailRegister() as registrar:
        result = registrar.register(email, password)
        
        if result['success']:
            logger.success("注册成功！")
            print(f"\n注册信息:")
            print(f"邮箱: {result['email']}")
            print(f"密码: {result['password']}")
        else:
            logger.error(f"注册失败: {result['message']}")
    
    return result

def main():
    """主函数"""
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)
    os.makedirs("screenshots", exist_ok=True)
    
    logger.info("DuckDuckGo 邮箱注册工具")
    logger.info("请选择操作模式:")
    print("1. 单个邮箱注册")
    print("2. 批量邮箱注册")
    print("3. 自定义邮箱注册")
    print("0. 退出")
    
    try:
        choice = input("请输入选择 (0-3): ").strip()
        
        if choice == "1":
            single_registration_example()
        elif choice == "2":
            count = input("请输入注册数量 (默认3): ").strip()
            count = int(count) if count.isdigit() else 3
            batch_registration_example(count)
        elif choice == "3":
            custom_registration_example()
        elif choice == "0":
            logger.info("退出程序")
        else:
            logger.error("无效选择")
            
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序执行错误: {e}")

if __name__ == "__main__":
    main()
