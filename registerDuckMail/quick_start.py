#!/usr/bin/env python3
"""
快速启动脚本 - DuckDuckGo 邮箱注册
"""
import os
import sys
from pathlib import Path

def check_installation():
    """检查安装状态"""
    try:
        from duck_email_register import DuckEmailRegister
        from utils import Utils
        from config import Config
        return True
    except ImportError:
        return False

def quick_install():
    """快速安装"""
    print("🚀 检测到首次运行，正在进行快速安装...")
    
    # 创建目录
    directories = ['logs', 'screenshots', 'results']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    # 创建 .env 文件
    env_example = Path(".env.example")
    env_file = Path(".env")
    if env_example.exists() and not env_file.exists():
        env_file.write_text(env_example.read_text())
    
    print("✅ 快速安装完成")

def interactive_menu():
    """交互式菜单"""
    print("\n" + "="*50)
    print("🦆 DuckDuckGo 邮箱注册工具")
    print("="*50)
    print("请选择操作:")
    print("1. 🎯 注册单个邮箱（随机生成）")
    print("2. 📧 注册指定邮箱")
    print("3. 📦 批量注册邮箱")
    print("4. 🧪 测试安装")
    print("5. 📖 查看帮助")
    print("0. 🚪 退出")
    print("-" * 50)
    
    while True:
        try:
            choice = input("请输入选择 (0-5): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                register_random_email()
            elif choice == "2":
                register_custom_email()
            elif choice == "3":
                batch_register()
            elif choice == "4":
                test_installation()
            elif choice == "5":
                show_help()
            else:
                print("❌ 无效选择，请输入 0-5")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"💥 程序错误: {e}")

def register_random_email():
    """注册随机邮箱"""
    print("\n🎯 注册随机邮箱")
    print("-" * 30)
    
    try:
        from duck_email_register import DuckEmailRegister
        from utils import Utils
        
        email = Utils.generate_random_email("gmail.com")
        password = Utils.generate_random_password()
        
        print(f"📧 生成邮箱: {email}")
        print(f"🔑 生成密码: {password}")
        
        confirm = input("\n确认注册？(y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 取消注册")
            return
        
        print("\n🚀 开始注册...")
        with DuckEmailRegister() as registrar:
            result = registrar.register(email, password)
            
            if result['success']:
                print("🎉 注册成功！")
                print(f"📧 邮箱: {result['email']}")
                print(f"🔑 密码: {result['password']}")
                
                # 保存结果
                with open('results/successful_registrations.txt', 'a', encoding='utf-8') as f:
                    f.write(f"{result['email']}:{result['password']}\n")
                print("💾 结果已保存到 results/successful_registrations.txt")
            else:
                print(f"❌ 注册失败: {result['message']}")
                
    except Exception as e:
        print(f"💥 注册过程出错: {e}")

def register_custom_email():
    """注册指定邮箱"""
    print("\n📧 注册指定邮箱")
    print("-" * 30)
    
    try:
        from duck_email_register import DuckEmailRegister
        from utils import Utils
        
        email = input("请输入邮箱地址: ").strip()
        if not email:
            print("❌ 邮箱地址不能为空")
            return
        
        if not Utils.validate_email(email):
            print("❌ 邮箱格式无效")
            return
        
        password = input("请输入密码（留空自动生成）: ").strip()
        if not password:
            password = Utils.generate_random_password()
            print(f"🔑 自动生成密码: {password}")
        
        print(f"\n📧 邮箱: {email}")
        print(f"🔑 密码: {password}")
        
        confirm = input("\n确认注册？(y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 取消注册")
            return
        
        print("\n🚀 开始注册...")
        with DuckEmailRegister() as registrar:
            result = registrar.register(email, password)
            
            if result['success']:
                print("🎉 注册成功！")
                print(f"📧 邮箱: {result['email']}")
                print(f"🔑 密码: {result['password']}")
                
                # 保存结果
                with open('results/successful_registrations.txt', 'a', encoding='utf-8') as f:
                    f.write(f"{result['email']}:{result['password']}\n")
                print("💾 结果已保存到 results/successful_registrations.txt")
            else:
                print(f"❌ 注册失败: {result['message']}")
                
    except Exception as e:
        print(f"💥 注册过程出错: {e}")

def batch_register():
    """批量注册"""
    print("\n📦 批量注册邮箱")
    print("-" * 30)
    
    try:
        count_str = input("请输入注册数量 (默认3): ").strip()
        count = int(count_str) if count_str.isdigit() else 3
        
        if count <= 0 or count > 20:
            print("❌ 注册数量应在 1-20 之间")
            return
        
        delay_str = input("请输入间隔时间/秒 (默认15): ").strip()
        delay = int(delay_str) if delay_str.isdigit() else 15
        
        print(f"\n📊 批量注册配置:")
        print(f"   数量: {count}")
        print(f"   间隔: {delay} 秒")
        
        confirm = input("\n确认开始批量注册？(y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 取消批量注册")
            return
        
        from duck_email_register import DuckEmailRegister
        from utils import Utils
        import time
        
        successful = 0
        failed = 0
        
        for i in range(count):
            print(f"\n🚀 正在注册第 {i+1}/{count} 个邮箱...")
            
            email = Utils.generate_random_email("gmail.com")
            password = Utils.generate_random_password()
            
            with DuckEmailRegister() as registrar:
                result = registrar.register(email, password)
                
                if result['success']:
                    successful += 1
                    print(f"✅ 第 {i+1} 个邮箱注册成功: {email}")
                    
                    # 保存结果
                    with open('results/successful_registrations.txt', 'a', encoding='utf-8') as f:
                        f.write(f"{result['email']}:{result['password']}\n")
                else:
                    failed += 1
                    print(f"❌ 第 {i+1} 个邮箱注册失败: {email}")
            
            # 延迟（除了最后一个）
            if i < count - 1:
                print(f"⏳ 等待 {delay} 秒...")
                time.sleep(delay)
        
        print(f"\n📊 批量注册完成:")
        print(f"   ✅ 成功: {successful}")
        print(f"   ❌ 失败: {failed}")
        print(f"   📁 结果保存在: results/successful_registrations.txt")
        
    except ValueError:
        print("❌ 请输入有效数字")
    except Exception as e:
        print(f"💥 批量注册出错: {e}")

def test_installation():
    """测试安装"""
    print("\n🧪 测试安装")
    print("-" * 30)
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, "test_setup.py"], 
                              capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
    except Exception as e:
        print(f"💥 测试失败: {e}")

def show_help():
    """显示帮助"""
    print("\n📖 帮助信息")
    print("-" * 30)
    print("🦆 DuckDuckGo 邮箱注册工具使用说明")
    print("\n📁 项目文件:")
    print("   - duck_email_register.py: 主注册模块")
    print("   - utils.py: 工具函数")
    print("   - config.py: 配置文件")
    print("   - example.py: 使用示例")
    print("   - run.py: 命令行工具")
    print("\n📂 目录结构:")
    print("   - logs/: 日志文件")
    print("   - screenshots/: 截图文件")
    print("   - results/: 注册结果")
    print("\n⚠️ 注意事项:")
    print("   - 请遵守 DuckDuckGo 服务条款")
    print("   - 避免频繁注册")
    print("   - 遇到验证码需手动处理")
    print("\n🔗 更多信息请查看 README.md")

def main():
    """主函数"""
    # 检查安装
    if not check_installation():
        print("❌ 检测到模块未安装，正在进行快速安装...")
        quick_install()
        print("✅ 快速安装完成，请运行 python install.py 进行完整安装")
        return
    
    # 快速设置
    quick_install()
    
    # 显示交互菜单
    interactive_menu()

if __name__ == "__main__":
    main()
