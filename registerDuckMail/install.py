#!/usr/bin/env python3
"""
DuckDuckGo 邮箱注册脚本安装器
"""
import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_python_version():
    """检查 Python 版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ 需要 Python 3.7 或更高版本")
        return False
    print(f"✅ Python 版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_dependencies():
    """安装依赖包"""
    print("📦 安装 Python 依赖包...")
    
    # 升级 pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级 pip"):
        return False
    
    # 安装依赖
    requirements_file = Path(__file__).parent / "requirements.txt"
    if requirements_file.exists():
        command = f"{sys.executable} -m pip install -r {requirements_file}"
        if not run_command(command, "安装依赖包"):
            return False
    else:
        # 手动安装主要依赖
        packages = [
            "DrissionPage>=4.0.0",
            "fake-useragent>=1.4.0",
            "loguru>=0.7.0",
            "python-dotenv>=1.0.0",
            "requests>=2.31.0"
        ]
        
        for package in packages:
            command = f"{sys.executable} -m pip install {package}"
            if not run_command(command, f"安装 {package}"):
                return False
    
    return True

def setup_directories():
    """创建必要的目录"""
    print("📁 创建目录结构...")
    directories = ['logs', 'screenshots', 'results']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ 创建目录: {directory}")
    
    return True

def setup_environment():
    """设置环境配置"""
    print("⚙️ 设置环境配置...")
    
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if env_example.exists() and not env_file.exists():
        try:
            env_file.write_text(env_example.read_text())
            print("✅ 创建 .env 配置文件")
        except Exception as e:
            print(f"⚠️ 创建 .env 文件失败: {e}")
    
    return True

def check_browser():
    """检查浏览器安装"""
    print("🌐 检查浏览器...")
    
    system = platform.system().lower()
    
    if system == "windows":
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        ]
    elif system == "darwin":  # macOS
        chrome_paths = [
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "/Applications/Chromium.app/Contents/MacOS/Chromium",
        ]
    else:  # Linux
        chrome_paths = [
            "/usr/bin/google-chrome",
            "/usr/bin/chromium-browser",
            "/usr/bin/chromium",
        ]
    
    for path in chrome_paths:
        if Path(path).exists():
            print(f"✅ 找到浏览器: {path}")
            return True
    
    print("⚠️ 未找到 Chrome/Chromium 浏览器")
    print("请安装 Google Chrome 或 Chromium 浏览器")
    return False

def test_installation():
    """测试安装"""
    print("🧪 测试安装...")
    
    try:
        # 测试导入主要模块
        from duck_email_register import DuckEmailRegister
        from utils import Utils
        from config import Config
        
        print("✅ 模块导入成功")
        
        # 测试基本功能
        email = Utils.generate_random_email()
        password = Utils.generate_random_password()
        
        print(f"✅ 功能测试成功")
        print(f"   示例邮箱: {email}")
        print(f"   示例密码: {password}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def print_usage_info():
    """打印使用说明"""
    print("\n" + "="*50)
    print("🎉 安装完成！")
    print("="*50)
    print("\n📖 使用方法:")
    print("1. 运行示例脚本:")
    print("   python example.py")
    print("\n2. 使用命令行工具:")
    print("   python run.py --help")
    print("\n3. 单个邮箱注册:")
    print("   python run.py")
    print("\n4. 批量邮箱注册:")
    print("   python run.py --batch 5")
    print("\n5. 指定邮箱注册:")
    print("   python run.py --email <EMAIL>")
    print("\n📁 重要文件:")
    print("   - config.py: 配置文件")
    print("   - .env: 环境变量配置")
    print("   - logs/: 日志文件目录")
    print("   - screenshots/: 截图文件目录")
    print("   - results/: 结果文件目录")
    print("\n⚠️ 注意事项:")
    print("   - 请遵守 DuckDuckGo 服务条款")
    print("   - 避免频繁注册，建议适当延迟")
    print("   - 遇到验证码时需要手动处理")
    print("\n🔗 更多信息请查看 README.md")

def main():
    """主安装流程"""
    print("🚀 DuckDuckGo 邮箱注册脚本安装器")
    print("="*40)
    
    # 检查 Python 版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        sys.exit(1)
    
    # 创建目录
    if not setup_directories():
        print("❌ 目录创建失败")
        sys.exit(1)
    
    # 设置环境
    setup_environment()
    
    # 检查浏览器
    browser_ok = check_browser()
    
    # 测试安装
    if not test_installation():
        print("❌ 安装测试失败")
        sys.exit(1)
    
    # 打印使用说明
    print_usage_info()
    
    if not browser_ok:
        print("\n⚠️ 警告: 未检测到浏览器，请手动安装 Chrome 或 Chromium")

if __name__ == "__main__":
    main()
