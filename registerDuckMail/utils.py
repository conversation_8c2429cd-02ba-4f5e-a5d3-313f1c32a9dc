"""
工具函数模块 - DuckDuckGo 邮箱注册脚本
"""
import random
import string
import time
import re
from typing import Optional, List
from loguru import logger
from fake_useragent import UserAgent

class Utils:
    """工具类"""
    
    @staticmethod
    def generate_random_username(length: int = 8) -> str:
        """生成随机用户名"""
        letters = string.ascii_lowercase
        return ''.join(random.choice(letters) for _ in range(length))
    
    @staticmethod
    def generate_random_email(domain: str = "gmail.com") -> str:
        """生成随机邮箱地址"""
        username = Utils.generate_random_username(random.randint(6, 12))
        return f"{username}@{domain}"
    
    @staticmethod
    def generate_random_password(length: int = 12) -> str:
        """生成随机密码"""
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(characters) for _ in range(length))
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def random_delay(min_seconds: float = 1.0, max_seconds: float = 3.0) -> None:
        """随机延迟"""
        delay = random.uniform(min_seconds, max_seconds)
        logger.debug(f"随机延迟 {delay:.2f} 秒")
        time.sleep(delay)
    
    @staticmethod
    def get_random_user_agent() -> str:
        """获取随机用户代理"""
        try:
            ua = UserAgent()
            return ua.random
        except Exception as e:
            logger.warning(f"获取随机用户代理失败: {e}")
            # 返回默认用户代理
            return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    @staticmethod
    def retry_on_exception(max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0):
        """重试装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                attempts = 0
                current_delay = delay
                
                while attempts < max_attempts:
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        attempts += 1
                        if attempts >= max_attempts:
                            logger.error(f"函数 {func.__name__} 执行失败，已达到最大重试次数 {max_attempts}")
                            raise e
                        
                        logger.warning(f"函数 {func.__name__} 执行失败 (第 {attempts} 次): {e}")
                        logger.info(f"等待 {current_delay:.2f} 秒后重试...")
                        time.sleep(current_delay)
                        current_delay *= backoff
                
                return None
            return wrapper
        return decorator
    
    @staticmethod
    def safe_click(page, selector: str, timeout: int = 10) -> bool:
        """安全点击元素"""
        try:
            element = page.ele(selector, timeout=timeout)
            if element:
                element.click()
                Utils.random_delay(0.5, 1.5)
                return True
            return False
        except Exception as e:
            logger.error(f"点击元素失败 {selector}: {e}")
            return False
    
    @staticmethod
    def safe_input(page, selector: str, text: str, timeout: int = 10) -> bool:
        """安全输入文本"""
        try:
            element = page.ele(selector, timeout=timeout)
            if element:
                element.clear()
                Utils.random_delay(0.2, 0.5)
                element.input(text)
                Utils.random_delay(0.5, 1.0)
                return True
            return False
        except Exception as e:
            logger.error(f"输入文本失败 {selector}: {e}")
            return False
    
    @staticmethod
    def wait_for_element(page, selector: str, timeout: int = 10) -> bool:
        """等待元素出现"""
        try:
            element = page.ele(selector, timeout=timeout)
            return element is not None
        except Exception as e:
            logger.error(f"等待元素失败 {selector}: {e}")
            return False
    
    @staticmethod
    def take_screenshot(page, filename: str = None) -> str:
        """截图"""
        try:
            if not filename:
                timestamp = int(time.time())
                filename = f"screenshot_{timestamp}.png"
            
            page.get_screenshot(filename)
            logger.info(f"截图已保存: {filename}")
            return filename
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return ""
